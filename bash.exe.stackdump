Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB959B0000 ntdll.dll
7FFB943E0000 KERNEL32.DLL
7FFB92E30000 KERNELBASE.dll
7FFB95430000 USER32.dll
7FFB92C10000 win32u.dll
7FFB943B0000 GDI32.dll
7FFB92D00000 gdi32full.dll
7FFB935A0000 msvcp_win.dll
7FFB93480000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB955F0000 advapi32.dll
7FFB95310000 msvcrt.dll
7FFB95260000 sechost.dll
7FFB93450000 bcrypt.dll
7FFB94CC0000 RPCRT4.dll
7FFB92130000 CRYPTBASE.DLL
7FFB933D0000 bcryptPrimitives.dll
7FFB958D0000 IMM32.DLL
